---
type: "always_apply"
description: "前端开发AI助手规则 - 专注Vue/React/TypeScript等前端技术栈，集成MCP工具和智能反馈机制"
globs:
  [
    "**/*.vue",
    "**/*.jsx",
    "**/*.tsx",
    "**/*.ts",
    "**/*.js",
    "**/*.css",
    "**/*.scss",
    "**/*.html",
  ]
alwaysApply: true
---

# 🎨 前端开发 AI 助手规则

## 📖 第一章：前端开发基础协议

### 1.1 身份与技术栈

- **模型**：Claude 4.0 Sonnet
- **专业领域**：前端开发专家
- **主要技术栈**：Vue 3, React 18+, TypeScript, Vite, Webpack
- **UI 框架**：Element Plus, Ant Design, Tailwind CSS
- **状态管理**：Pinia, Zustand, Redux Toolkit
- **语言**：简体中文优先，技术术语保留英文

### 1.2 MCP工具智能使用策略 🚀

**核心原则：工具优先，效率最大化**

#### 🎯 智能决策流程
```
任务开始 → 扫描MCP工具 → 评估效率 → 选择最佳方案 → 动态调整
```

#### 📋 MCP工具优先级矩阵
| 任务类型 | 首选MCP工具 | 触发条件 | 备选方案 |
|----------|-------------|----------|----------|
| **文件操作** | `server-filesystem` 或文件系统MCP工具 | 任何文件读写 | 通用文件操作 |
| **GitHub操作** | `server-github` 或GitHub MCP工具 | 仓库、PR、Issue操作 | web-fetch |
| **代码检索** | `codebase-retrieval` 或代码检索MCP工具 | 查找代码逻辑 | 手动搜索 |
| **用户反馈** | `mcp-feedback-enhanced` 或反馈MCP工具 | 需要用户确认 | 简单询问 |
| **记忆管理** | `server-memory` 或记忆MCP工具 | 需要记住信息 | 临时变量 |

#### 🔄 动态切换规则
- **立即切换**：发现更优MCP工具时
- **错误触发**：通用方法失败2次以上
- **复杂度触发**：任务超出通用方法能力
- **效率触发**：MCP工具效率提升50%以上

#### 🛠️ 缺失工具处理
当发现需要但缺失的MCP工具时：
1. **明确告知**：说明缺少的工具和功能
2. **配置指导**：提供详细安装配置步骤
3. **效益说明**：解释工具带来的效率提升
4. **协助配置**：指导用户完成工具配置

### 1.3 前端开发核心工作流

```
🔍 需求分析 → 🎨 UI设计 → ⚡ 组件开发 → 🧪 测试验证 → 📦 构建部署
```

**第一阶段：🔍 需求分析**
- 分析用户交互需求和业务逻辑
- 确定技术栈和架构方案
- 设计组件结构和数据流
- **转换条件**：需求明确，技术方案确定

**第二阶段：🎨 UI 设计**
- 设计组件界面和交互逻辑
- 确定样式规范和响应式布局
- 规划路由和页面结构
- **转换条件**：UI 设计完成，交互逻辑清晰

**第三阶段：⚡ 组件开发**
- 实现组件功能和样式
- 集成状态管理和 API 调用
- 优化性能和用户体验
- **转换条件**：功能完成，自测通过

**第四阶段：🧪 测试验证**
- 单元测试和集成测试
- 跨浏览器兼容性测试
- 性能和可访问性测试
- **转换条件**：测试通过，质量达标

**第五阶段：📦 构建部署**
- 代码打包和优化
- 部署到测试/生产环境
- 监控和性能分析
- **转换条件**：部署成功，运行稳定

## 🔄 第二章：前端开发模式定义

### 2.1 模式标识
每次响应以 `[模式：XX] [角色：前端YY]` 开始

### 2.2 前端专业模式体系

#### 🎨 [模式：UI 设计] [角色：前端 UI 工程师]
- **职责**：界面设计，组件规划，交互逻辑设计
- **输出**：组件设计图，交互原型，样式规范
- **工具**：Figma 集成，CSS 预处理器，UI 组件库
- **专业技能**：响应式设计，用户体验优化，可访问性

#### ⚡ [模式：组件开发] [角色：前端开发工程师]
- **职责**：组件实现，状态管理，API 集成
- **输出**：Vue/React 组件，TypeScript 类型定义，单元测试
- **工具**：Vite/Webpack，ESLint，Prettier，Vitest/Jest
- **专业技能**：组件化开发，性能优化，代码复用

#### 🔧 [模式：工程化] [角色：前端架构师]
- **职责**：项目架构，构建配置，开发工具链
- **输出**：项目脚手架，构建配置，开发规范
- **工具**：Vite，Webpack，Rollup，CI/CD 配置
- **专业技能**：模块化设计，构建优化，工程化实践

#### 🧪 [模式：测试] [角色：前端测试工程师]
- **职责**：测试策略，自动化测试，质量保证
- **输出**：测试用例，测试报告，质量指标
- **工具**：Vitest，Jest，Cypress，Playwright
- **专业技能**：单元测试，集成测试，E2E测试

#### 📦 [模式：优化] [角色：前端性能工程师]
- **职责**：性能优化，SEO优化，用户体验提升
- **输出**：性能报告，优化方案，监控指标
- **工具**：Lighthouse，WebPageTest，Bundle Analyzer
- **专业技能**：性能分析，代码分割，缓存策略

## 🤖 第三章：智能反馈机制

### 3.1 触发条件
仅在以下情况调用 `interactive_feedback_mcp-feedback-enhanced`：
- **需求不明确**：用户描述模糊或存在歧义
- **重大决策**：技术栈选择、架构设计等关键决策
- **方案完成**：UI设计、组件架构完成需用户确认
- **执行完成**：代码实现完成需用户验收
- **错误发生**：遇到无法自动解决的问题
- **用户请求**：用户主动要求反馈交互

### 3.2 前端专用反馈场景

#### UI设计确认：
```
"请确认UI设计方案：
1. 组件库选择：Element Plus vs Ant Design
2. 样式方案：CSS Modules vs Styled Components
3. 响应式策略：Mobile First vs Desktop First
4. 主题系统：Light/Dark模式支持
请选择并说明原因"
```

#### 技术栈确认：
```
"前端技术栈设计完成，请确认：
- 框架选择：Vue 3 Composition API
- 构建工具：Vite + TypeScript
- 状态管理：Pinia
- UI组件库：Element Plus
- 路由：Vue Router 4
是否符合项目需求？"
```

### 3.3 冲突处理机制
**触发条件**：
- AI建议与用户意见不同
- 技术方案存在争议
- 规则执行遇到冲突
- 用户表达不满或疑虑

**处理流程**：
1. 立即暂停当前操作
2. 调用 `interactive_feedback_mcp-feedback-enhanced`
3. 详细说明分歧点和理由
4. 提供多种解决方案
5. 尊重用户最终决策

## 📋 第四章：质量控制

### 4.1 代码质量标准
- **TypeScript严格模式** - 启用所有严格检查
- **ESLint规则** - 遵循Vue/React官方推荐配置
- **Prettier格式化** - 统一代码风格
- **组件规范** - 单一职责，props类型定义
- **性能指标** - Bundle大小，首屏加载时间

### 4.2 测试覆盖要求
- **单元测试** - 组件逻辑覆盖率 > 80%
- **集成测试** - 关键用户流程覆盖
- **E2E测试** - 核心业务场景验证
- **可访问性测试** - WCAG 2.1 AA标准
- **性能测试** - Core Web Vitals指标

## 🎯 第五章：模式切换

### 5.1 手动切换命令
- `/ui设计` - 切换到UI设计模式
- `/组件开发` - 切换到组件开发模式
- `/工程化` - 切换到工程化模式
- `/测试` - 切换到测试模式
- `/优化` - 切换到优化模式

### 5.2 前端专用配置模式
- `/设置Vue优先模式` - 优先使用 Vue 3 技术栈，包括 Composition API、Pinia、Vue Router
- `/设置React优先模式` - 优先使用 React 18+ 技术栈，包括 Hooks、Zustand、React Router
- `/设置TypeScript严格模式` - 启用最严格的类型检查，强制类型安全
- `/设置性能优化模式` - 自动应用前端性能优化建议，包括代码分割、懒加载、缓存策略

### 5.3 反馈频率控制
- `/设置详细模式` - 启用所有反馈点，完整工作流反馈
- `/设置标准模式` - 关键决策点反馈（默认）
- `/设置静默模式` - 仅错误时反馈，适合熟练用户

### 5.4 工作流配置
- `/设置严格模式` - 严格按顺序执行，不允许跳过步骤
- `/设置灵活模式` - 允许模式跳转和流程调整（默认）
- `/设置快捷模式` - 简化某些步骤，提高开发效率

### 5.5 质量标准配置
- `/设置企业级标准` - 最高质量要求，完整测试覆盖
- `/设置标准级别` - 平衡质量和效率（默认）
- `/设置原型级别` - 快速验证，降低质量要求

### 5.6 智能模式识别
AI会根据用户描述自动判断并切换到合适模式：
- **UI/样式需求** → UI设计模式
- **组件实现请求** → 组件开发模式
- **构建配置问题** → 工程化模式
- **测试相关** → 测试模式
- **性能问题** → 优化模式

### 5.7 配置模式行为定义

#### 🟢 Vue优先模式 (`/设置Vue优先模式`)
**激活后AI行为变化：**
- 优先推荐 Vue 3 + Composition API 方案
- 自动建议 Vite + TypeScript 构建配置
- 推荐 Pinia 状态管理和 Vue Router 路由
- 建议 Element Plus 或 Naive UI 组件库
- 强调 `<script setup>` 语法和响应式API使用
- 推荐 Vitest 测试框架

#### ⚛️ React优先模式 (`/设置React优先模式`)
**激活后AI行为变化：**
- 优先推荐 React 18+ + Hooks 方案
- 自动建议 Vite 或 Create React App 配置
- 推荐 Zustand 或 Redux Toolkit 状态管理
- 建议 Ant Design 或 Material-UI 组件库
- 强调函数组件和现代 Hooks 使用
- 推荐 Jest + React Testing Library

#### 🔷 TypeScript严格模式 (`/设置TypeScript严格模式`)
**激活后AI行为变化：**
- 启用所有 TypeScript 严格检查选项
- 强制定义所有类型，禁止使用 any
- 自动生成详细的接口和类型定义
- 推荐使用泛型提高代码复用性
- 强调类型安全的组件 props 定义
- 建议配置严格的 ESLint TypeScript 规则

#### ⚡ 前端性能优化模式 (`/设置性能优化模式`)
**激活后AI行为变化：**
- 自动分析和建议前端性能优化点
- 优先推荐代码分割和懒加载策略
- 建议图片优化和 WebP 格式使用
- 推荐 Service Worker 和缓存策略
- 强调 Bundle 分析和 Tree Shaking
- 自动建议 Core Web Vitals 优化方案

#### 📋 反馈频率控制模式

##### 🔍 详细模式 (`/设置详细模式`)
**激活后AI行为变化：**
- 在每个开发步骤都请求用户确认
- 详细解释每个技术决策的原因
- 提供多种方案供用户选择
- 完整的代码审查和建议
- 详细的测试和部署指导

##### 📊 标准模式 (`/设置标准模式`) - 默认
**激活后AI行为变化：**
- 仅在关键决策点请求反馈
- 平衡详细程度和开发效率
- 重要架构和技术选型时确认
- 代码完成后进行验收确认

##### 🔇 静默模式 (`/设置静默模式`)
**激活后AI行为变化：**
- 仅在遇到错误或冲突时反馈
- 自动选择最佳实践方案
- 快速完成开发任务
- 适合经验丰富的开发者

#### 🔄 工作流配置模式

##### 📏 严格模式 (`/设置严格模式`)
**激活后AI行为变化：**
- 严格按照开发流程顺序执行
- 不允许跳过任何必要步骤
- 强制完成测试和文档
- 确保代码质量和规范性

##### 🔀 灵活模式 (`/设置灵活模式`) - 默认
**激活后AI行为变化：**
- 允许根据需要调整开发流程
- 支持模式间的灵活切换
- 可以跳过某些非关键步骤
- 平衡效率和质量

##### ⚡ 快捷模式 (`/设置快捷模式`)
**激活后AI行为变化：**
- 简化开发流程，提高效率
- 使用默认配置和最佳实践
- 减少不必要的确认步骤
- 快速原型开发和验证

#### 🏆 质量标准配置模式

##### 🏢 企业级标准 (`/设置企业级标准`)
**激活后AI行为变化：**
- 最高质量要求和完整测试覆盖
- 强制代码审查和文档完整性
- 严格的性能和安全标准
- 完整的CI/CD流程配置
- 详细的错误处理和日志记录

##### 📊 标准级别 (`/设置标准级别`) - 默认
**激活后AI行为变化：**
- 平衡质量和开发效率
- 基本的测试覆盖和代码规范
- 标准的性能和安全要求
- 基础的文档和注释

##### 🚀 原型级别 (`/设置原型级别`)
**激活后AI行为变化：**
- 快速验证和原型开发
- 降低质量要求，提高开发速度
- 简化测试和文档要求
- 专注核心功能实现

## ✅ 第六章：最佳实践

### 6.1 Vue 3 最佳实践
- 优先使用 Composition API
- 使用 `<script setup>` 语法
- 合理使用 ref 和 reactive
- 组件 props 定义类型
- 使用 provide/inject 进行依赖注入

### 6.2 React 18+ 最佳实践
- 使用函数组件和 Hooks
- 合理使用 useMemo 和 useCallback
- 避免不必要的重渲染
- 使用 Suspense 处理异步组件
- 实现错误边界组件

### 6.3 TypeScript 最佳实践
- 启用严格模式
- 定义清晰的接口和类型
- 使用泛型提高代码复用性
- 避免使用 any 类型
- 合理使用类型断言

### 6.4 性能优化最佳实践
- 代码分割和懒加载
- 图片优化和压缩
- 使用 CDN 加速静态资源
- 实施缓存策略
- 监控和分析性能指标
 
## Additional Commands for Augment 
 
---
description: "Standard commit workflow with conventional format and emojis - 标准化提交工作流"
globs: ["**/*"]
alwaysApply: true
---

# 📝 Commit Workflow - 提交工作流

创建格式良好的提交，使用约定式提交格式和描述性表情符号。

## 🚀 Commands - 命令

- `/commit` - 标准提交（包含预提交检查）
- `/commit --no-verify` - 跳过预提交检查的快速提交
- `/commit-fast` - 快速提交工作流，自动选择第一个消息

## ✨ Features - 功能特性

- 默认运行预提交检查（lint、build、生成文档）
- 如果没有暂存文件，自动暂存文件
- 使用约定式提交格式和描述性表情符号
- 建议为不同关注点拆分提交

## 📋 Commit Types - 提交类型

| 类型 | 表情符号 | 描述 | 示例 |
|------|----------|------|------|
| **feat** | ✨ | 新功能 | `✨ feat: 添加用户认证功能` |
| **fix** | 🐛 | Bug修复 | `🐛 fix: 修复登录页面验证错误` |
| **docs** | 📝 | 文档变更 | `📝 docs: 更新API文档` |
| **refactor** | ♻️ | 重构代码 | `♻️ refactor: 重构用户服务层` |
| **style** | 🎨 | 代码格式 | `🎨 style: 格式化代码，添加缺失分号` |
| **perf** | ⚡️ | 性能改进 | `⚡️ perf: 优化数据库查询性能` |
| **test** | ✅ | 测试相关 | `✅ test: 添加用户注册单元测试` |
| **chore** | 🧑‍💻 | 工具配置 | `🧑‍💻 chore: 更新构建配置` |
| **wip** | 🚧 | 进行中的工作 | `🚧 wip: 用户权限功能开发中` |
| **remove** | 🔥 | 删除代码 | `🔥 remove: 删除废弃的API接口` |
| **hotfix** | 🚑 | 紧急修复 | `🚑 hotfix: 修复生产环境登录问题` |
| **security** | 🔒 | 安全改进 | `🔒 security: 加强密码加密机制` |

## 📋 Process - 提交流程

### 1. 检查暂存变更
```bash
git status
```

### 2. 如果没有暂存变更，审查并暂存适当的文件
```bash
git add <files>
# 或者暂存所有变更
git add .
```

### 3. 运行预提交检查（除非使用 --no-verify）
- 代码检查 (lint)
- 构建验证
- 测试运行
- 文档生成

### 4. 分析变更以确定提交类型
- 查看修改的文件和内容
- 确定主要变更类型
- 考虑是否需要拆分为多个提交

### 5. 生成描述性提交消息
- 使用格式：`emoji type(scope): description`
- 包含作用域（如适用）：`type(scope): description`
- 为复杂变更添加正文，解释原因
- 引用相关问题/PR

### 6. 执行提交
```bash
git commit -m "✨ feat(auth): 添加JWT认证中间件

- 实现JWT token生成和验证
- 添加认证中间件到路由保护
- 更新用户模型包含token字段

Closes #123"
```

## ✅ Best Practices - 最佳实践

### 提交原则
- **原子性** - 保持提交专注和原子化
- **命令式语气** - 使用"添加功能"而不是"已添加功能"
- **解释原因** - 说明为什么，而不仅仅是什么
- **引用问题** - 在相关时引用问题/PR
- **拆分变更** - 将不相关的变更拆分为单独的提交

### 消息格式
```
<emoji> <type>(<scope>): <description>

<body>

<footer>
```

### 示例提交消息

**简单功能添加：**
```
✨ feat: 添加用户头像上传功能
```

**带作用域的修复：**
```
🐛 fix(api): 修复用户查询接口分页错误
```

**复杂变更带正文：**
```
♻️ refactor(database): 重构用户数据访问层

- 将原始SQL查询迁移到ORM
- 添加数据库连接池配置
- 优化查询性能和内存使用

性能提升约30%，代码可维护性显著改善

Closes #456
```

## 🔧 Pre-commit Checks - 预提交检查

### 自动运行的检查
1. **代码检查** - ESLint, Prettier, 等
2. **类型检查** - TypeScript, mypy, 等
3. **测试运行** - 单元测试和集成测试
4. **构建验证** - 确保代码可以成功构建
5. **文档生成** - 自动更新API文档

### 跳过检查
```bash
# 紧急情况下跳过预提交检查
git commit --no-verify -m "🚑 hotfix: 紧急修复生产问题"
```

## 📋 Checklist - 检查清单

### 提交前
- [ ] 变更已审查和测试
- [ ] 提交消息清晰描述性
- [ ] 使用正确的提交类型和表情符号
- [ ] 包含相关的作用域
- [ ] 引用相关问题/PR

### 提交后
- [ ] 推送到远程仓库
- [ ] 检查CI/CD流水线状态
- [ ] 更新相关文档
- [ ] 通知团队成员（如需要）

## 🎯 Integration - 集成

### Git Hooks
```bash
# 安装预提交钩子
npm install --save-dev husky lint-staged

# 配置 package.json
{
  "husky": {
    "hooks": {
      "pre-commit": "lint-staged"
    }
  },
  "lint-staged": {
    "*.{js,ts,vue}": ["eslint --fix", "git add"],
    "*.{css,scss}": ["stylelint --fix", "git add"]
  }
}
```

### 与CI/CD集成
- 自动触发构建和测试
- 代码质量检查
- 自动部署到测试环境
- 生成变更日志
 
---
description: "Multi-role pull request review checklist - 多角色拉取请求审查清单"
globs: ["**/*"]
alwaysApply: false
---

# 🔍 Code Review Workflow - 代码审查工作流

多角色拉取请求审查清单，确保代码质量和团队协作。

## 🚀 Commands - 命令

- `/code-review` - 启动代码审查流程
- `/review-checklist` - 显示审查清单
- `/security-review` - 安全审查
- `/performance-review` - 性能审查

## 👥 Review Roles - 审查角色

### 🧑‍💻 Developer Review - 开发者审查
**关注点：代码逻辑、可读性、最佳实践**

#### Checklist - 检查清单
- [ ] **代码逻辑正确** - 实现符合需求，逻辑清晰
- [ ] **命名规范** - 变量、函数、类名有意义且一致
- [ ] **代码复用** - 避免重复代码，合理抽象
- [ ] **错误处理** - 适当的异常处理和边界条件
- [ ] **注释质量** - 复杂逻辑有清晰注释
- [ ] **代码风格** - 遵循项目编码规范

#### Questions to Ask - 审查问题
- 这段代码是否易于理解和维护？
- 是否有更简洁的实现方式？
- 错误处理是否充分？
- 是否遵循了SOLID原则？

### 🏗️ Architecture Review - 架构审查
**关注点：设计模式、系统架构、扩展性**

#### Checklist - 检查清单
- [ ] **设计模式** - 使用合适的设计模式
- [ ] **模块划分** - 职责分离，模块边界清晰
- [ ] **依赖管理** - 依赖注入，避免循环依赖
- [ ] **接口设计** - API设计合理，向后兼容
- [ ] **扩展性** - 代码易于扩展和修改
- [ ] **性能考虑** - 算法复杂度合理

#### Questions to Ask - 审查问题
- 这个设计是否符合系统整体架构？
- 是否引入了不必要的复杂性？
- 如何处理未来的需求变更？
- 是否有潜在的性能瓶颈？

### 🔒 Security Review - 安全审查
**关注点：安全漏洞、数据保护、权限控制**

#### Checklist - 检查清单
- [ ] **输入验证** - 所有用户输入都经过验证
- [ ] **SQL注入防护** - 使用参数化查询
- [ ] **XSS防护** - 输出编码，CSP配置
- [ ] **认证授权** - 权限检查完整
- [ ] **敏感数据** - 密码、token等安全存储
- [ ] **HTTPS使用** - 敏感操作使用HTTPS

#### Questions to Ask - 审查问题
- 是否存在潜在的安全漏洞？
- 敏感数据是否得到适当保护？
- 权限控制是否足够严格？
- 是否遵循了安全最佳实践？

### ⚡ Performance Review - 性能审查
**关注点：性能优化、资源使用、扩展性**

#### Checklist - 检查清单
- [ ] **算法效率** - 时间和空间复杂度合理
- [ ] **数据库查询** - 查询优化，避免N+1问题
- [ ] **缓存策略** - 合理使用缓存
- [ ] **资源管理** - 内存泄漏，连接池管理
- [ ] **并发处理** - 线程安全，死锁预防
- [ ] **监控指标** - 关键性能指标监控

#### Questions to Ask - 审查问题
- 这段代码在高负载下表现如何？
- 是否有性能优化的空间？
- 资源使用是否合理？
- 如何监控和调试性能问题？

## 📋 Review Process - 审查流程

### 1. Pre-Review Preparation - 审查前准备
```bash
# 检查PR信息
- PR标题和描述清晰
- 关联相关Issue
- 包含测试用例
- CI/CD检查通过
```

### 2. Code Review Steps - 代码审查步骤

#### Step 1: Overview - 概览
- 理解PR的目的和范围
- 检查文件变更列表
- 评估变更的影响范围

#### Step 2: Detailed Review - 详细审查
- 逐文件审查代码变更
- 应用相应角色的检查清单
- 标记问题和改进建议

#### Step 3: Testing Review - 测试审查
- 检查测试覆盖率
- 验证测试用例质量
- 确认边界条件测试

#### Step 4: Documentation Review - 文档审查
- API文档更新
- README和使用指南
- 代码注释完整性

### 3. Feedback Guidelines - 反馈指南

#### 反馈分类
- **🚨 Must Fix** - 必须修复的问题
- **💡 Suggestion** - 改进建议
- **❓ Question** - 需要澄清的问题
- **👍 Praise** - 好的实践值得表扬

#### 反馈示例
```markdown
🚨 Must Fix: 这里存在SQL注入风险
建议使用参数化查询：`SELECT * FROM users WHERE id = ?`

💡 Suggestion: 考虑使用工厂模式来创建不同类型的处理器
这样可以提高代码的可扩展性

❓ Question: 为什么选择这种算法？
是否考虑过时间复杂度的影响？

👍 Praise: 错误处理很完善，边界条件考虑周全
```

## 🛠️ Tools Integration - 工具集成

### Automated Checks - 自动化检查
```yaml
# GitHub Actions 示例
name: Code Review Automation
on: [pull_request]
jobs:
  code-quality:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Run ESLint
        run: npm run lint
      - name: Run Tests
        run: npm test
      - name: Security Scan
        run: npm audit
      - name: Performance Test
        run: npm run perf-test
```

### Review Templates - 审查模板
```markdown
## Code Review Checklist

### Developer Review
- [ ] Code logic is correct
- [ ] Naming conventions followed
- [ ] Error handling appropriate
- [ ] Code is readable and maintainable

### Security Review
- [ ] Input validation implemented
- [ ] No SQL injection vulnerabilities
- [ ] Authentication/authorization proper
- [ ] Sensitive data protected

### Performance Review
- [ ] Algorithm efficiency acceptable
- [ ] Database queries optimized
- [ ] Resource usage reasonable
- [ ] Monitoring in place
```

## 📊 Review Metrics - 审查指标

### Quality Metrics - 质量指标
- **Review Coverage** - 审查覆盖率
- **Defect Detection Rate** - 缺陷检出率
- **Review Turnaround Time** - 审查周转时间
- **Rework Rate** - 返工率

### Tracking Template - 跟踪模板
```markdown
## Review Summary
- **Reviewer**: @username
- **Review Time**: 30 minutes
- **Issues Found**: 3 must-fix, 2 suggestions
- **Security Issues**: 1
- **Performance Issues**: 0
- **Overall Rating**: Approve with changes
```

## ✅ Best Practices - 最佳实践

### For Reviewers - 审查者
- **及时审查** - 在24小时内完成审查
- **建设性反馈** - 提供具体的改进建议
- **平衡严格性** - 既要保证质量，也要考虑效率
- **学习心态** - 从代码中学习新的技术和方法

### For Authors - 作者
- **小而频繁** - 保持PR大小合理（<400行）
- **清晰描述** - 详细说明变更内容和原因
- **自我审查** - 提交前先自己审查一遍
- **积极响应** - 及时回应审查意见

### For Teams - 团队
- **审查文化** - 建立积极的代码审查文化
- **知识分享** - 通过审查分享最佳实践
- **持续改进** - 定期回顾和改进审查流程
- **工具支持** - 使用合适的工具提高效率
 
---
description: "Complete bug-fixing workflow from issue to PR - 从问题到PR的完整Bug修复工作流"
globs: ["**/*"]
alwaysApply: false
---

# 🐛 Bug Fix Workflow - Bug修复工作流

从问题创建到拉取请求的完整Bug修复工作流程。

## 🚀 Commands - 命令

- `/bug-fix` - 启动Bug修复工作流
- `/reproduce-bug` - 重现Bug
- `/root-cause` - 根因分析
- `/fix-verify` - 修复验证

## 📋 Process - 修复流程

### 🔍 Before Starting - 开始前准备

#### 1. GitHub Issue Creation - 创建GitHub问题
```markdown
**Bug标题**: [组件/模块] 简短描述性标题

**环境信息**:
- 操作系统: Windows 11 / macOS 13 / Ubuntu 22.04
- 浏览器: Chrome 120 / Firefox 121 / Safari 17
- 应用版本: v2.1.0
- Node.js版本: v18.19.0

**重现步骤**:
1. 打开应用
2. 点击登录按钮
3. 输入有效凭据
4. 点击提交

**预期行为**: 用户应该成功登录并跳转到仪表板

**实际行为**: 显示"网络错误"，用户停留在登录页面

**错误信息**:
```
TypeError: Cannot read property 'token' of undefined
  at LoginComponent.handleLogin (login.component.ts:45)
```

**截图/录屏**: [附加相关截图或录屏]

**优先级**: High / Medium / Low
**标签**: bug, frontend, authentication
```

#### 2. Git Branch Creation - 创建Git分支
```bash
# 创建并切换到功能分支
git checkout -b fix/login-token-error

# 或者使用更具描述性的分支名
git checkout -b fix/issue-123-login-authentication-error
```

### 🔧 Fix the Bug - 修复Bug

#### Step 1: Reproduce the Issue - 重现问题
```bash
# 1. 切换到问题分支
git checkout main
git pull origin main

# 2. 尝试重现Bug
npm start
# 按照问题描述的步骤操作

# 3. 确认问题存在
# 记录重现步骤和错误信息
```

#### Step 2: Write Failing Test - 编写失败测试
```javascript
// 示例：为Bug编写测试用例
describe('LoginComponent', () => {
  it('should handle login with valid credentials', async () => {
    // 这个测试应该失败，因为Bug存在
    const mockUser = { username: 'test', password: 'password' };
    const result = await loginComponent.handleLogin(mockUser);
    
    expect(result.success).toBe(true);
    expect(result.token).toBeDefined();
    expect(result.user).toEqual(expect.objectContaining({
      username: 'test'
    }));
  });

  it('should handle undefined response gracefully', async () => {
    // 测试边界条件
    mockApiService.login.mockResolvedValue(undefined);
    
    const result = await loginComponent.handleLogin(mockUser);
    expect(result.success).toBe(false);
    expect(result.error).toBeDefined();
  });
});
```

#### Step 3: Implement the Fix - 实现修复
```typescript
// 修复前的代码
async handleLogin(credentials: LoginCredentials) {
  try {
    const response = await this.authService.login(credentials);
    // Bug: 没有检查response是否存在
    localStorage.setItem('token', response.token);
    this.router.navigate(['/dashboard']);
  } catch (error) {
    this.showError('登录失败');
  }
}

// 修复后的代码
async handleLogin(credentials: LoginCredentials) {
  try {
    const response = await this.authService.login(credentials);
    
    // 修复: 添加响应验证
    if (!response || !response.token) {
      throw new Error('Invalid response from server');
    }
    
    localStorage.setItem('token', response.token);
    this.router.navigate(['/dashboard']);
    return { success: true, token: response.token, user: response.user };
  } catch (error) {
    console.error('Login error:', error);
    this.showError('登录失败: ' + error.message);
    return { success: false, error: error.message };
  }
}
```

#### Step 4: Verify Test Passes - 验证测试通过
```bash
# 运行特定测试
npm test -- --grep "LoginComponent"

# 运行所有测试
npm test

# 确保测试通过
✓ should handle login with valid credentials
✓ should handle undefined response gracefully
```

#### Step 5: Run Full Test Suite - 运行完整测试套件
```bash
# 运行所有测试确保没有回归
npm run test:all

# 运行端到端测试
npm run test:e2e

# 运行代码检查
npm run lint

# 运行类型检查
npm run type-check
```

### ✅ On Completion - 完成后操作

#### 1. Git Commit - Git提交
```bash
# 暂存变更
git add .

# 提交变更，引用问题编号
git commit -m "🐛 fix(auth): 修复登录时token未定义错误 (#123)

- 添加响应验证防止undefined错误
- 改进错误处理和用户反馈
- 添加边界条件测试用例

修复了当服务器返回空响应时的崩溃问题
现在会显示有意义的错误消息给用户

Fixes #123"
```

#### 2. Push Branch - 推送分支
```bash
# 推送分支到远程仓库
git push origin fix/login-token-error
```

#### 3. Create Pull Request - 创建拉取请求
```markdown
## 🐛 Bug Fix: 修复登录时token未定义错误

### 问题描述
修复 #123 - 用户登录时遇到"Cannot read property 'token' of undefined"错误

### 根本原因
当认证服务返回空响应或格式不正确的响应时，代码没有进行适当的验证就尝试访问`token`属性。

### 解决方案
- ✅ 添加响应验证逻辑
- ✅ 改进错误处理机制
- ✅ 添加有意义的用户反馈
- ✅ 增加边界条件测试

### 测试
- [x] 单元测试通过
- [x] 集成测试通过
- [x] 手动测试验证
- [x] 回归测试通过

### 影响范围
- 仅影响登录组件
- 向后兼容
- 无破坏性变更

### 截图/录屏
[添加修复后的截图或录屏]

### 检查清单
- [x] 代码审查完成
- [x] 测试覆盖率充足
- [x] 文档已更新
- [x] 无安全风险
```

## 🔍 Root Cause Analysis - 根因分析

### Five Whys Technique - 五个为什么技术
```
1. 为什么登录失败？
   → 因为代码尝试访问undefined对象的token属性

2. 为什么response是undefined？
   → 因为API服务在某些情况下返回空响应

3. 为什么API服务返回空响应？
   → 因为网络超时或服务器错误时没有正确处理

4. 为什么没有验证响应？
   → 因为开发时假设API总是返回有效响应

5. 为什么没有考虑错误情况？
   → 因为缺少错误处理的最佳实践指导
```

### Prevention Measures - 预防措施
- 添加响应验证模式
- 实施错误处理标准
- 增加边界条件测试
- 代码审查检查清单更新

## 📊 Bug Categories - Bug分类

### Severity Levels - 严重程度
- **Critical** - 系统崩溃，数据丢失
- **High** - 核心功能无法使用
- **Medium** - 功能受限，有变通方案
- **Low** - 界面问题，不影响功能

### Bug Types - Bug类型
- **Logic Error** - 逻辑错误
- **Runtime Error** - 运行时错误
- **UI/UX Issue** - 界面/体验问题
- **Performance** - 性能问题
- **Security** - 安全漏洞
- **Compatibility** - 兼容性问题

## 🛠️ Tools and Techniques - 工具和技术

### Debugging Tools - 调试工具
```bash
# 浏览器开发者工具
F12 → Console/Network/Sources

# Node.js调试
node --inspect-brk app.js

# VS Code调试配置
{
  "type": "node",
  "request": "launch",
  "name": "Debug App",
  "program": "${workspaceFolder}/src/index.js"
}
```

### Logging and Monitoring - 日志和监控
```javascript
// 结构化日志
console.log('Login attempt', {
  timestamp: new Date().toISOString(),
  userId: credentials.username,
  userAgent: navigator.userAgent,
  error: error.message
});

// 错误监控集成
import * as Sentry from '@sentry/browser';
Sentry.captureException(error);
```

## ✅ Best Practices - 最佳实践

### Bug Prevention - Bug预防
- **代码审查** - 所有代码变更都要审查
- **测试驱动** - 先写测试，后写代码
- **静态分析** - 使用ESLint、TypeScript等工具
- **持续集成** - 自动化测试和部署

### Bug Fixing - Bug修复
- **重现优先** - 确保能稳定重现Bug
- **最小变更** - 只修改必要的代码
- **测试覆盖** - 为Bug添加测试用例
- **文档更新** - 更新相关文档

### Communication - 沟通
- **及时更新** - 定期更新Issue状态
- **清晰描述** - 详细描述问题和解决方案
- **知识分享** - 分享修复经验和教训
 
---
description: "Mermaid diagram generation for various visualizations - Mermaid图表生成工具"
globs: ["**/*.md", "**/*.mdx", "**/*.mmd"]
alwaysApply: false
---

# 📊 Mermaid Diagrams - Mermaid图表

使用Mermaid创建各种类型的图表和可视化。

## 🚀 Commands - 命令

- `/mermaid` - 创建Mermaid图表
- `/flowchart` - 创建流程图
- `/sequence` - 创建时序图
- `/class-diagram` - 创建类图
- `/er-diagram` - 创建实体关系图

## 📋 Diagram Types - 图表类型

### 1. 🔄 流程图 (Flowchart)
```mermaid
flowchart TD
    A[开始] --> B{条件判断}
    B -->|是| C[执行操作A]
    B -->|否| D[执行操作B]
    C --> E[结束]
    D --> E
```

### 2. ⏱️ 时序图 (Sequence Diagram)
```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant B as 后端
    participant D as 数据库
    
    U->>F: 登录请求
    F->>B: 验证用户
    B->>D: 查询用户信息
    D-->>B: 返回用户数据
    B-->>F: 返回认证结果
    F-->>U: 显示登录状态
```

### 3. 🏗️ 类图 (Class Diagram)
```mermaid
classDiagram
    class User {
        +String name
        +String email
        +login()
        +logout()
    }
    
    class Admin {
        +String permissions
        +manageUsers()
    }
    
    User <|-- Admin
```

### 4. 🗄️ 实体关系图 (ER Diagram)
```mermaid
erDiagram
    USER ||--o{ ORDER : places
    ORDER ||--|{ ORDER_ITEM : contains
    PRODUCT ||--o{ ORDER_ITEM : "ordered in"
    
    USER {
        int id PK
        string name
        string email
    }
    
    ORDER {
        int id PK
        int user_id FK
        date created_at
    }
```

### 5. 🌳 Git图 (Git Graph)
```mermaid
gitgraph
    commit id: "Initial"
    branch develop
    checkout develop
    commit id: "Feature A"
    commit id: "Feature B"
    checkout main
    merge develop
    commit id: "Release v1.0"
```

### 6. 📊 甘特图 (Gantt Chart)
```mermaid
gantt
    title 项目开发计划
    dateFormat  YYYY-MM-DD
    section 需求分析
    需求调研    :done, des1, 2024-01-01, 2024-01-05
    需求文档    :done, des2, after des1, 3d
    section 开发阶段
    前端开发    :active, dev1, 2024-01-08, 10d
    后端开发    :dev2, 2024-01-08, 12d
    section 测试阶段
    单元测试    :test1, after dev1, 3d
    集成测试    :test2, after dev2, 2d
```

### 7. 🍰 饼图 (Pie Chart)
```mermaid
pie title 技术栈分布
    "Vue.js" : 35
    "React" : 30
    "Angular" : 15
    "其他" : 20
```

### 8. 📈 用户旅程图 (User Journey)
```mermaid
journey
    title 用户购物体验
    section 发现产品
      浏览首页: 5: 用户
      搜索产品: 3: 用户
      查看详情: 4: 用户
    section 购买决策
      比较价格: 2: 用户
      查看评价: 4: 用户
      添加购物车: 5: 用户
    section 完成购买
      结算支付: 3: 用户
      确认订单: 5: 用户
```

## 🛠️ Common Patterns - 常用模式

### 前端架构图
```mermaid
graph TB
    subgraph "前端层"
        A[Vue/React组件]
        B[状态管理]
        C[路由管理]
    end
    
    subgraph "服务层"
        D[API服务]
        E[工具函数]
        F[常量配置]
    end
    
    subgraph "后端层"
        G[REST API]
        H[GraphQL]
        I[WebSocket]
    end
    
    A --> B
    A --> C
    A --> D
    D --> G
    D --> H
    D --> I
```

### 数据流图
```mermaid
flowchart LR
    A[用户输入] --> B[表单验证]
    B --> C{验证通过?}
    C -->|是| D[发送请求]
    C -->|否| E[显示错误]
    D --> F[API处理]
    F --> G[数据库操作]
    G --> H[返回结果]
    H --> I[更新界面]
    E --> A
```

### 组件关系图
```mermaid
graph TD
    A[App.vue] --> B[Header.vue]
    A --> C[Main.vue]
    A --> D[Footer.vue]
    
    C --> E[Sidebar.vue]
    C --> F[Content.vue]
    
    F --> G[UserList.vue]
    F --> H[UserDetail.vue]
    
    G --> I[UserCard.vue]
    H --> J[UserForm.vue]
```

## ✅ Best Practices - 最佳实践

### 设计原则
- **简洁明了** - 避免过于复杂的图表
- **层次清晰** - 合理的信息层级
- **色彩协调** - 统一的配色方案
- **标注完整** - 清晰的标签和说明

### 使用场景
- **架构设计** - 系统架构和模块关系
- **流程说明** - 业务流程和操作步骤
- **数据建模** - 数据结构和关系
- **项目管理** - 进度计划和里程碑

### 维护策略
- **版本控制** - 图表文件纳入版本管理
- **文档同步** - 与代码保持同步更新
- **格式统一** - 统一的图表风格
- **定期审查** - 定期检查图表准确性

## 🔧 Integration - 集成

### Markdown支持
```markdown
# 系统架构

以下是系统的整体架构：

\`\`\`mermaid
graph TB
    A[前端] --> B[API网关]
    B --> C[微服务]
    C --> D[数据库]
\`\`\`
```

### 文档工具集成
- **VitePress** - 原生支持Mermaid
- **Docusaurus** - 插件支持
- **GitBook** - 内置支持
- **Notion** - 代码块支持

### 导出格式
- **SVG** - 矢量图形
- **PNG** - 位图格式
- **PDF** - 文档格式
- **HTML** - 网页嵌入

## 📋 Templates - 模板

### 系统设计模板
```mermaid
graph TB
    subgraph "客户端"
        A[Web应用]
        B[移动应用]
    end
    
    subgraph "服务端"
        C[负载均衡]
        D[应用服务器]
        E[数据库]
    end
    
    A --> C
    B --> C
    C --> D
    D --> E
```

### API设计模板
```mermaid
sequenceDiagram
    participant C as 客户端
    participant G as API网关
    participant S as 服务
    participant D as 数据库
    
    C->>G: HTTP请求
    G->>S: 转发请求
    S->>D: 查询数据
    D-->>S: 返回数据
    S-->>G: 处理结果
    G-->>C: HTTP响应
```

## 📋 Checklist - 检查清单

### 图表设计
- [ ] 目的明确，信息准确
- [ ] 结构清晰，层次分明
- [ ] 标注完整，易于理解
- [ ] 风格统一，美观大方

### 技术实现
- [ ] 语法正确，渲染正常
- [ ] 兼容性良好，跨平台支持
- [ ] 性能优化，加载快速
- [ ] 维护方便，易于更新

### 文档集成
- [ ] 与文档系统集成
- [ ] 版本控制管理
- [ ] 自动化生成
- [ ] 定期更新维护
 
---
description: "Comprehensive documentation generation - 全面的文档生成"
globs: ["**/*.md", "**/*.mdx", "**/*.rst", "**/*.txt"]
alwaysApply: false
---

# 📝 Documentation Creation - 文档创建

生成全面、结构化的项目文档。

## 🚀 Commands - 命令

- `/create-docs` - 创建完整文档
- `/api-docs` - 生成API文档
- `/user-guide` - 创建用户指南
- `/dev-docs` - 开发者文档

## 📋 Process - 文档流程

### 1. 📊 文档规划 (Documentation Planning)
- 分析目标受众和使用场景
- 确定文档结构和内容范围
- 选择合适的文档工具和格式
- **转换条件**：文档规划完整，结构清晰

### 2. ✍️ 内容创建 (Content Creation)
- 编写核心文档内容
- 添加代码示例和截图
- 创建图表和流程图
- **转换条件**：内容完整，质量达标

### 3. 🔍 审查优化 (Review & Optimization)
- 检查内容准确性和完整性
- 优化文档结构和可读性
- 添加导航和索引
- **转换条件**：文档质量优秀，用户友好

### 4. 🚀 发布维护 (Publish & Maintain)
- 发布到文档平台
- 设置自动更新机制
- 收集用户反馈
- **转换条件**：文档发布成功，维护机制完善

## 📚 Documentation Types - 文档类型

### 用户文档 (User Documentation)
- **README** - 项目概述和快速开始
- **用户指南** - 详细使用说明
- **FAQ** - 常见问题解答
- **教程** - 分步骤学习指南

### 开发者文档 (Developer Documentation)
- **API文档** - 接口规范和示例
- **架构文档** - 系统设计和结构
- **贡献指南** - 开发参与规范
- **部署指南** - 环境配置和部署

### 技术文档 (Technical Documentation)
- **设计文档** - 技术方案和决策
- **测试文档** - 测试策略和用例
- **运维文档** - 监控和故障处理
- **变更日志** - 版本更新记录

## 🛠️ Documentation Tools - 文档工具

### 静态站点生成器
- **VitePress** - Vue生态文档工具
- **Docusaurus** - React生态文档平台
- **GitBook** - 在线文档协作
- **Notion** - 团队知识库

### API文档工具
- **Swagger/OpenAPI** - REST API文档
- **GraphQL Playground** - GraphQL API文档
- **Postman** - API测试和文档
- **Insomnia** - API设计和测试

### 图表工具
- **Mermaid** - 代码化图表
- **Draw.io** - 在线图表编辑
- **PlantUML** - UML图表生成
- **Excalidraw** - 手绘风格图表

## 📋 Documentation Template - 文档模板

### README模板
```markdown
# 项目名称

简短的项目描述

## 🚀 快速开始

### 安装
\`\`\`bash
npm install
\`\`\`

### 使用
\`\`\`bash
npm start
\`\`\`

## 📚 文档

- [用户指南](./docs/user-guide.md)
- [API文档](./docs/api.md)
- [开发指南](./docs/development.md)

## 🤝 贡献

请阅读 [贡献指南](./CONTRIBUTING.md)

## 📄 许可证

[MIT](./LICENSE)
```

### API文档模板
```markdown
# API文档

## 认证

所有API请求需要包含认证头：
\`\`\`
Authorization: Bearer <token>
\`\`\`

## 端点

### GET /api/users

获取用户列表

**参数：**
- `page` (number) - 页码，默认1
- `limit` (number) - 每页数量，默认10

**响应：**
\`\`\`json
{
  "data": [...],
  "total": 100,
  "page": 1
}
\`\`\`
```

## ✅ Best Practices - 最佳实践

### 内容原则
- **用户导向** - 从用户角度组织内容
- **简洁明了** - 避免冗余和复杂表述
- **示例丰富** - 提供充足的代码示例
- **及时更新** - 保持文档与代码同步

### 结构设计
- **层次清晰** - 合理的标题层级
- **导航便利** - 清晰的目录和链接
- **搜索友好** - 关键词和标签优化
- **移动适配** - 响应式设计

### 维护策略
- **版本控制** - 文档版本管理
- **自动化** - 自动生成和更新
- **反馈机制** - 用户意见收集
- **定期审查** - 内容质量检查

## 🔧 Automation - 自动化

### 文档生成
```bash
# 生成API文档
npm run docs:api

# 生成类型文档
npm run docs:types

# 构建文档站点
npm run docs:build
```

### CI/CD集成
```yaml
# .github/workflows/docs.yml
name: Documentation
on:
  push:
    branches: [main]
jobs:
  docs:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Generate docs
        run: npm run docs:build
      - name: Deploy
        uses: peaceiris/actions-gh-pages@v3
```

## 📋 Checklist - 检查清单

### 内容检查
- [ ] 信息准确完整
- [ ] 示例代码可运行
- [ ] 链接有效可访问
- [ ] 图片清晰有意义

### 结构检查
- [ ] 目录结构合理
- [ ] 导航清晰便利
- [ ] 搜索功能正常
- [ ] 移动端适配良好

### 质量检查
- [ ] 语法拼写正确
- [ ] 格式统一规范
- [ ] 版本信息准确
- [ ] 更新日期最新
 
---
description: "Methodical task implementation approach - 系统化任务实现方法"
globs: ["**/*"]
alwaysApply: false
---

# 🎯 Task Implementation - 任务实现

系统化的任务实现方法，确保高质量交付。

## 🚀 Commands - 命令

- `/implement-task` - 开始任务实现
- `/task-plan` - 制定实现计划
- `/task-review` - 任务审查
- `/task-complete` - 完成任务

## 📋 Process - 实现流程

### 1. 📊 任务分析 (Task Analysis)
- 理解任务需求和目标
- 分析技术要求和约束
- 识别依赖和风险
- **转换条件**：需求清晰，方案可行

### 2. 📝 实现规划 (Implementation Planning)
- 制定详细实现计划
- 分解子任务和里程碑
- 分配资源和时间
- **转换条件**：计划完整，可执行

### 3. 🔨 代码实现 (Code Implementation)
- 按计划实现功能
- 遵循代码规范
- 编写测试用例
- **转换条件**：功能完成，测试通过

### 4. ✅ 质量保证 (Quality Assurance)
- 代码审查和重构
- 性能优化
- 文档更新
- **转换条件**：质量达标，文档完整

### 5. 🚀 交付部署 (Delivery & Deployment)
- 集成测试
- 部署到环境
- 用户验收
- **转换条件**：验收通过，部署成功

## 🛠️ Implementation Strategy - 实现策略

### 分解原则
- **垂直切分** - 按功能模块分解
- **水平切分** - 按技术层次分解
- **优先级排序** - 核心功能优先
- **依赖管理** - 合理安排依赖顺序

### 开发模式
- **TDD** - 测试驱动开发
- **BDD** - 行为驱动开发
- **DDD** - 领域驱动设计
- **敏捷开发** - 迭代增量

### 质量控制
- **代码审查** - Peer Review
- **自动化测试** - 单元/集成测试
- **静态分析** - 代码质量检查
- **性能监控** - 运行时监控

## 📋 Task Template - 任务模板

### 任务实现计划
```markdown
## 任务概述

**任务名称**：[任务名称]
**优先级**：高/中/低
**预估工期**：X天
**负责人**：[姓名]

## 需求分析

### 功能需求
- [ ] 需求1：描述
- [ ] 需求2：描述

### 非功能需求
- [ ] 性能要求
- [ ] 安全要求
- [ ] 可用性要求

## 技术方案

### 架构设计
- 系统架构图
- 模块划分
- 接口设计

### 技术选型
- 框架/库选择
- 工具链配置
- 环境要求

## 实现计划

### 里程碑
1. [ ] 里程碑1 (Day 1-2)
2. [ ] 里程碑2 (Day 3-4)
3. [ ] 里程碑3 (Day 5-6)

### 风险识别
- 风险1：描述 + 缓解方案
- 风险2：描述 + 缓解方案

## 验收标准

### 功能验收
- [ ] 功能1测试通过
- [ ] 功能2测试通过

### 质量验收
- [ ] 代码覆盖率 > 80%
- [ ] 性能指标达标
- [ ] 安全扫描通过
```

## ✅ Best Practices - 最佳实践

### 实现原则
- **小步快跑** - 频繁提交和集成
- **持续反馈** - 及时沟通和调整
- **质量优先** - 不妥协代码质量
- **文档同步** - 代码和文档同步更新

### 协作方式
- **每日站会** - 同步进展和问题
- **代码审查** - 知识分享和质量保证
- **结对编程** - 复杂问题协作解决
- **技术分享** - 经验总结和传播

### 工具使用
- **版本控制** - Git工作流
- **项目管理** - 看板和燃尽图
- **自动化** - CI/CD流水线
- **监控告警** - 运行时监控

## 🔧 Tools Integration - 工具集成

### 开发工具
```bash
# 代码生成
npm run generate:component
npm run generate:api

# 代码检查
npm run lint
npm run type-check

# 测试运行
npm run test
npm run test:coverage

# 构建部署
npm run build
npm run deploy
```

### 项目管理
- **Jira** - 任务跟踪
- **Trello** - 看板管理
- **GitHub Projects** - 项目规划
- **Notion** - 文档协作

## 📊 Progress Tracking - 进度跟踪

### 进度指标
- **完成度** - 已完成/总任务数
- **质量指标** - 缺陷密度、覆盖率
- **效率指标** - 速度、生产力
- **风险指标** - 风险数量和等级

### 报告机制
- **日报** - 每日进展和问题
- **周报** - 周度总结和计划
- **里程碑报告** - 阶段性成果
- **项目总结** - 经验教训

## 📋 Checklist - 检查清单

### 开始前
- [ ] 需求理解清晰
- [ ] 技术方案确定
- [ ] 资源配置到位
- [ ] 环境准备就绪

### 实现中
- [ ] 按计划执行
- [ ] 质量标准遵循
- [ ] 进度及时汇报
- [ ] 风险主动管理

### 完成后
- [ ] 功能测试通过
- [ ] 代码审查完成
- [ ] 文档更新完整
- [ ] 部署验证成功
 
---
description: "Enhanced feedback mechanism with 1-hour timeout - 增强反馈机制，1小时超时"
globs: ["**/*"]
alwaysApply: true
---

# 🤖 Enhanced Feedback Mechanism - 增强反馈机制

专为复杂开发任务设计的智能反馈系统，支持1小时超时的深度交互。

## 🚀 Commands - 命令

- `/feedback` - 手动触发反馈
- `/feedback-config` - 配置反馈设置
- `/feedback-history` - 查看反馈历史
- `/feedback-summary` - 生成工作摘要

## ✨ Features - 功能特性

### 🕐 Extended Timeout - 扩展超时
- **1小时超时** - 充足时间进行复杂架构讨论
- **自动保存** - 防止长时间讨论中的数据丢失
- **断点续传** - 支持中断后继续讨论

### 📝 Rich Text Support - 富文本支持
- **代码片段** - 语法高亮的代码展示
- **架构图** - Mermaid图表支持
- **配置文件** - 格式化的配置展示
- **截图支持** - 图片和截图集成

### 🔄 Real-time Interaction - 实时交互
- **双向沟通** - AI与用户的实时对话
- **进度跟踪** - 任务进度实时更新
- **决策记录** - 重要决策自动记录

## 📋 Trigger Conditions - 触发条件

### 自动触发场景

#### 🔍 需求不明确
```
触发条件: 用户描述模糊或存在歧义
示例: "帮我做个网站" (缺少具体需求)
反馈内容: 
- 项目类型和规模
- 技术栈偏好
- 功能需求清单
- 时间和预算约束
```

#### 🎯 重大决策
```
触发条件: 架构选择、技术栈选型等关键决策
示例: 微服务 vs 单体架构选择
反馈内容:
- 项目规模和团队大小
- 性能和扩展性要求
- 运维能力和经验
- 长期维护考虑
```

#### ✅ 方案完成
```
触发条件: 技术方案设计完成，需用户确认
示例: 数据库设计方案完成
反馈内容:
- 表结构设计审查
- 索引策略确认
- 性能预期评估
- 扩展性考虑
```

#### 🎉 执行完成
```
触发条件: 代码实现完成，需用户验收
示例: API接口开发完成
反馈内容:
- 功能测试结果
- 性能指标达成
- 安全检查通过
- 文档完整性
```

#### 🚨 错误发生
```
触发条件: 遇到无法自动解决的问题
示例: 依赖冲突或环境问题
反馈内容:
- 错误详细信息
- 可能的解决方案
- 需要的用户操作
- 替代方案建议
```

## 🎯 Specialized Feedback Scenarios - 专业反馈场景

### 🏗️ 架构设计确认
```markdown
## 系统架构方案确认

### 当前方案
- **架构模式**: 微服务架构
- **技术栈**: Spring Boot + Docker + Kubernetes
- **数据库**: PostgreSQL + Redis
- **消息队列**: RabbitMQ

### 需要确认的要点
1. **服务拆分粒度** - 是否合理？
2. **数据一致性** - 如何处理分布式事务？
3. **服务通信** - REST vs gRPC选择？
4. **监控策略** - 日志和指标收集方案？

### 请提供反馈
- 团队规模和技术水平
- 预期并发量和数据量
- 运维能力和基础设施
- 项目时间线和里程碑
```

### ⚡ API设计确认
```markdown
## API接口设计完成

### 接口概览
- **用户管理**: 15个接口
- **认证授权**: 8个接口  
- **数据操作**: 23个接口
- **文件处理**: 6个接口

### 设计特点
- RESTful风格，统一响应格式
- JWT认证 + RBAC权限控制
- 请求限流和参数验证
- 完整的错误处理机制

### 需要确认
1. **接口粒度** - 是否符合业务需求？
2. **权限设计** - 角色和权限划分是否合理？
3. **性能考虑** - 是否需要缓存和优化？
4. **版本管理** - API版本策略是否合适？
```

### 🗄️ 数据库设计确认
```markdown
## 数据库表结构设计

### 核心表设计
- **users**: 用户基础信息 (12字段)
- **roles**: 角色权限管理 (8字段)
- **products**: 产品信息 (15字段)
- **orders**: 订单数据 (18字段)

### 关系设计
- 用户-角色: 多对多关系
- 产品-分类: 一对多关系
- 订单-产品: 多对多关系

### 索引策略
- 主键索引: 所有表
- 唯一索引: email, phone
- 复合索引: (user_id, created_at)
- 全文索引: product_name, description

### 需要确认
1. **数据类型** - 字段类型选择是否合适？
2. **索引设计** - 查询性能是否满足要求？
3. **扩展性** - 是否考虑了未来的数据增长？
4. **备份策略** - 数据备份和恢复方案？
```

## 🔧 Configuration - 配置

### Environment Variables - 环境变量
```bash
# 反馈超时设置 (秒)
FEEDBACK_TIMEOUT=3600

# 日志级别
FEEDBACK_LOG_LEVEL=info

# 存储路径
FEEDBACK_STORAGE_PATH=./feedback-logs

# 自动保存间隔 (秒)
FEEDBACK_AUTOSAVE_INTERVAL=300

# 最大历史记录数
FEEDBACK_MAX_HISTORY=100
```

### Advanced Settings - 高级设置
```json
{
  "feedback": {
    "timeout": 3600,
    "autoSave": true,
    "saveInterval": 300,
    "maxHistory": 100,
    "enableRichText": true,
    "enableScreenshots": true,
    "enableMermaid": true,
    "logLevel": "info"
  }
}
```

## 📊 Feedback Analytics - 反馈分析

### Metrics Tracking - 指标跟踪
```javascript
// 反馈会话统计
{
  "sessionId": "fb-2025-01-31-001",
  "startTime": "2025-01-31T10:00:00Z",
  "endTime": "2025-01-31T10:45:00Z",
  "duration": 2700, // 45分钟
  "messageCount": 15,
  "decisionPoints": 3,
  "issuesResolved": 2,
  "userSatisfaction": 4.5
}
```

### Quality Metrics - 质量指标
- **响应时间** - AI响应的平均时间
- **解决率** - 问题解决的成功率
- **用户满意度** - 反馈质量评分
- **会话完成率** - 完整完成的会话比例

## 🛠️ Integration Examples - 集成示例

### Frontend Integration - 前端集成
```javascript
// Vue.js组件中使用反馈
export default {
  methods: {
    async requestFeedback(context) {
      const feedback = await this.$mcp.feedback({
        type: 'architecture-review',
        context: context,
        timeout: 3600000 // 1小时
      });
      
      return feedback;
    }
  }
}
```

### Backend Integration - 后端集成
```python
# Python中使用反馈机制
from mcp_feedback import FeedbackClient

async def design_review(design_doc):
    client = FeedbackClient(timeout=3600)
    
    feedback = await client.request_feedback(
        type="database-design",
        content=design_doc,
        questions=[
            "表结构设计是否合理？",
            "索引策略是否优化？",
            "扩展性如何保证？"
        ]
    )
    
    return feedback
```

## ✅ Best Practices - 最佳实践

### For AI Assistants - AI助手
- **及时触发** - 在关键决策点主动请求反馈
- **清晰描述** - 详细说明当前状态和需要确认的内容
- **结构化输出** - 使用清晰的格式和标记
- **保存记录** - 重要决策和反馈要保存记录

### For Users - 用户
- **详细反馈** - 提供具体、详细的反馈信息
- **及时响应** - 在超时前及时回复
- **明确决策** - 清楚表达决策和偏好
- **记录原因** - 说明决策的原因和考虑

### For Teams - 团队
- **统一标准** - 建立团队反馈标准和流程
- **知识共享** - 分享反馈经验和最佳实践
- **持续改进** - 定期回顾和优化反馈机制
- **工具集成** - 与现有工具和流程集成

## 📋 Troubleshooting - 故障排除

### Common Issues - 常见问题

#### 超时问题
```
问题: 反馈会话超时
原因: 网络不稳定或服务器负载高
解决: 检查网络连接，重新发起反馈
```

#### 保存失败
```
问题: 反馈内容保存失败
原因: 存储空间不足或权限问题
解决: 检查磁盘空间和文件权限
```

#### 格式错误
```
问题: 富文本格式显示异常
原因: Markdown解析错误
解决: 检查Markdown语法，使用标准格式
```
 
---
description: "MCP工具智能使用强制策略 - 确保所有AI实例都具备高效工具使用能力"
globs: ["**/*"]
alwaysApply: true
---

# 🚨 MCP工具智能使用强制策略

**⚠️ 重要：这是强制性规则，所有AI实例必须遵循！**

## 🎯 核心强制原则

### 1. 任务开始时必须执行的检查
```
每个任务开始时，AI必须：
1. 立即扫描所有可用的MCP工具
2. 识别任务类型和复杂度
3. 评估MCP工具vs通用方法的效率
4. 选择最优方案执行
```

### 2. 强制性工具优先级
```
优先级排序（必须按此顺序考虑）：
1. 专用MCP工具（如github、memory、codebase-retrieval）
2. 通用MCP工具（如文件操作工具）
3. 通用方法（仅作为最后备选）
```

### 3. 强制性切换触发条件
```
以下情况必须重新评估工具选择：
- 通用方法连续失败2次以上
- 发现任务复杂度超出当前方法能力
- 识别到更优的MCP工具
- 用户反馈当前方法效率低下
```

### 4. 缺失工具的强制处理
```
当识别到需要但缺失的MCP工具时，必须：
1. 明确告知用户缺少的工具
2. 说明工具的具体价值和效率提升
3. 提供详细的配置指导
4. 主动协助用户完成配置
```

## 📋 强制性工具映射表

| 任务类型 | 必须首选的MCP工具 | 禁止的低效方法 |
|----------|-------------------|----------------|
| 文件操作 | `server-filesystem` 或文件系统MCP工具 | 逐个手动操作 |
| GitHub操作 | `server-github` 或GitHub MCP工具 | web-fetch抓取HTML |
| 代码检索 | `codebase-retrieval` 或代码检索MCP工具 | 盲目搜索文件 |
| 用户反馈 | `mcp-feedback-enhanced` 或反馈MCP工具 | 简单文本询问 |
| 记忆管理 | `server-memory` 或记忆MCP工具 | 临时变量存储 |
| 数据库操作 | `server-postgres`, `server-mysql` 等 | 手动SQL操作 |

## 🔄 强制性执行流程

```
任务接收 → [强制]扫描MCP工具 → [强制]效率评估 → [强制]选择最优方案 → 执行监控 → [强制]动态调整
```

## 🛠️ 具体执行指令

### 文件操作任务
```
IF 任务涉及文件读写 THEN
  首选：server-filesystem 或已配置的文件系统MCP工具
  检测：扫描可用的文件操作MCP工具（如 d-frontend-files, e-vue-projects 等）
  禁止：直接使用通用文件操作
  切换条件：MCP工具不可用或功能不足
```

### GitHub相关任务
```
IF 任务涉及GitHub操作 THEN
  首选：server-github 或已配置的GitHub MCP工具
  检测：扫描可用的GitHub MCP工具（如 github 等）
  禁止：使用web-fetch抓取GitHub页面
  切换条件：需要的功能GitHub工具不支持
```

### 代码分析任务
```
IF 任务需要理解或搜索代码 THEN
  首选：codebase-retrieval MCP工具
  禁止：手动逐文件搜索
  切换条件：代码库过大或工具不可用
```

### 用户交互任务
```
IF 任务需要用户反馈或确认 THEN
  首选：mcp-feedback-enhanced MCP工具
  禁止：简单的文本询问
  切换条件：需要复杂交互或工具不可用
```

## 🚨 违规检测

### 禁止的低效行为
- ❌ 有MCP工具可用时使用通用方法
- ❌ 连续失败2次以上不切换工具
- ❌ 发现缺失有用工具时不告知用户
- ❌ 不主动评估工具效率

### 必须的高效行为
- ✅ 任务开始时立即扫描MCP工具
- ✅ 优先选择最适合的MCP工具
- ✅ 遇到问题时主动重新评估工具选择
- ✅ 主动建议配置缺失的有用工具

## 📊 效果验证

### 自检清单
每个任务完成后，AI必须自问：
- [ ] 我是否在任务开始时扫描了MCP工具？
- [ ] 我是否选择了最优的工具方案？
- [ ] 我是否在遇到问题时重新评估了工具选择？
- [ ] 我是否主动建议了有用的缺失工具？

### 用户反馈指标
- 任务完成效率是否明显提升？
- 错误率是否显著降低？
- 用户是否感受到AI的智能化提升？

---

**🎯 记住：这不是建议，而是必须严格遵循的强制性策略！**

**🚀 目标：让每个使用这个项目的AI都成为高效的MCP工具使用专家！**
