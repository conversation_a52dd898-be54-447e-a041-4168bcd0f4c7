{"mcpServers": {"filesystem": {"command": "npx", "args": ["@modelcontextprotocol/server-filesystem", "E:\\API_VUE3"], "env": {}, "description": "文件系统操作 - 读写文件、创建目录等"}, "memory": {"command": "npx", "args": ["@modelcontextprotocol/server-memory"], "env": {}, "description": "记忆管理 - 保存和检索重要信息"}, "github": {"command": "npx", "args": ["@modelcontextprotocol/server-github"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": ""}, "description": "GitHub集成 - 仓库操作、提交代码等（需要配置TOKEN）"}, "feedback-enhanced": {"command": "uvx", "args": ["mcp-feedback-enhanced@latest", "server"], "env": {}, "description": "智能反馈增强 - 交互式确认和用户输入"}}, "version": "1.0.0", "description": "Vue 3项目MCP工具配置 - Rules 2.1 Optimized", "project": "API_VUE3"}